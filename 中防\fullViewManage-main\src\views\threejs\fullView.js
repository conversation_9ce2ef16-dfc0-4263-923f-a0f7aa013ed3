import {
  Scene,
  WebGLRenderer,
  PerspectiveCamera,
  MeshBasicMaterial,
  Mesh,
  SphereGeometry,
  TextureLoader,
  DoubleSide,
  ClampToEdgeWrapping,
  LinearFilter
} from 'three'
import {
  OrbitControls
} from 'three/examples/jsm/controls/OrbitControls'
import {
  textureLoaderHandle
} from './config'
import { TiledPanoramaManager } from './tiledPanorama'
import PanoSlicer from './PanoSlicer'

function isTiledLoading(path) {
  // 如果路径不是以图片后缀结尾，认为是分片加载
  return !/\.(jpg|jpeg|png|webp)$/i.test(path);
}

export default class fullView {
  onMouseWheelCb = ''
  constructor(container) {
    /**创建场景 */
    this.scene = new Scene();

    /**创建渲染器 */
    this.renderer = new WebGLRenderer();
    this.renderer.setPixelRatio(window.devicePixelRatio);
    this.renderer.setSize(container.clientWidth, container.clientHeight)
    container.appendChild(this.renderer.domElement)

    this.container = container

    /**创建相机 */
    this.camera = new PerspectiveCamera(75, container.width / container.height, 0.1, 100);
    this.camera.aspect = container.clientWidth / container.clientHeight
    // this.camera.position.set(0, 0, 0)

    /**最小和最大视角 */
    this.minFov = 0.1
    /**最小和最大视角 */
    this.maxFov = 180
    const that = this

    /**水平视角限制 */
    this.minAzimuthAngle = {
      get() {
        return this.value
      },
      value: -Infinity,
      set(val) {
        this.value = val
        if (that.controls) {
          if (val == -180 && that.maxAzimuthAngle.value == 180) {
            that.controls.minAzimuthAngle = -Infinity
          } else {
            that.controls.maxAzimuthAngle = Math.PI * that.maxAzimuthAngle.value / 180
            that.controls.minAzimuthAngle = Math.PI * val / 180
          }
        }
      }
    }; // radians
    /**水平视角限制 */
    this.maxAzimuthAngle = {
      get() {
        return this.value
      },
      value: Infinity,
      set(val) {
        this.value = val
        if (that.controls) {
          if (val == 180 && that.minAzimuthAngle.value == -180) {
            that.controls.maxAzimuthAngle = Infinity
          } else {
            that.controls.minAzimuthAngle = Math.PI * that.minAzimuthAngle.value / 180
            that.controls.maxAzimuthAngle = Math.PI * val / 180
          }
        }
      }
    }; // radians

    /**垂直视角限制 */
    this.minPolarAngle = {
      get() {
        return this.value
      },
      value: 0,
      set(val) {
        this.value = val
        if (that.controls) {
          that.controls.minPolarAngle = Math.PI * (val + 90) / 180
        }
      }
    }; // radians
    /**垂直视角限制 */
    this.maxPolarAngle = {
      get() {
        return this.value
      },
      value: Math.PI,
      set(val) {
        this.value = val
        if (that.controls) {
          that.controls.maxPolarAngle = Math.PI * (val + 90) / 180
        }
      }
    }; // radians

    /**创建控制器 */
    this.controls = new OrbitControls(this.camera, this.renderer.domElement);
    this.controls.enableZoom = false
    this.renderer.domElement.addEventListener('wheel', this.onMouseWheel.bind(this), {
      passive: false
    });
    
    // 初始化分片加载器
    this.tiledPanoramaManager = null;
    
    // 是否使用分片加载
    this.useTiledLoading = false;
    
    // 是否使用KRPano格式
    this.useKrpanoFormat = true;
    
    // 初始化全景切片工具
    this.panoSlicer = new PanoSlicer();
    
    this.render()
  }

  async onMouseWheel(event) {
    event.preventDefault();
    if (event.deltaY < 0 && this.camera.fov > this.minFov) {
      this.camera.fov--;
    } else if (event.deltaY > 0 && this.camera.fov < this.maxFov) {
      this.camera.fov++;
    }
    this.onMouseWheelCb && this.onMouseWheelCb()
    this.controls.update()
    
    // 如果使用分片加载，更新可见瓦片
    if (this.useTiledLoading && this.tiledPanoramaManager) {
      await this.tiledPanoramaManager.updateVisibleTiles();
    }
    
    this.render()
  }
  
  /**
   * 加载全景内容
   * @param {string} url - 全景图URL或包含分片的文件夹路径
   */
  async initContent(url) {
    console.log("🚀 ~ file: fullView.js ~ initContent ~ url", url, this.contentTextureUrl);
    
    // 如果URL没有变化，且非强制模式下，不重新加载
    if (url === this.contentTextureUrl ) {
      return;
    }
    
    this.contentTextureUrl = url;
    
    // 如果存在旧的分片加载器，清理资源
    if (this.tiledPanoramaManager) {
      this.tiledPanoramaManager.dispose();
      this.tiledPanoramaManager = null;
    }
    
    // 如果存在旧的球体，移除它
    if (this.sphere) {
      this.scene.remove(this.sphere);
      this.sphere.material.dispose();
      this.sphere.geometry.dispose();
      this.sphere = null;
    }
    
    try {
      // 根据是否使用分片加载选择不同的加载方式
      if (isTiledLoading(url)) {
        console.log("使用分片加载模式");
        
        // 创建分片加载器
        this.tiledPanoramaManager = new TiledPanoramaManager(this.scene, this.camera, {
          // 使用当前URL作为基础路径
          basePath: url,
          tilesPrefix: 'l',
          fileExtension: '.jpg',
          useKrpanoFormat: this.useKrpanoFormat,
          levels: 3
        });
        
        console.log("分片加载参数:", {
          basePath: url,
          tilesPrefix: 'l',
          fileExtension: '.jpg',
          useKrpanoFormat: this.useKrpanoFormat,
          levels: 3
        });
        
        // 初始化分片全景
        await this.tiledPanoramaManager.initPanorama(url);
        
        // 设置控制器变化事件，更新可见瓦片
        this.controls.addEventListener('change', async () => {
          if (this.tiledPanoramaManager) {
            await this.tiledPanoramaManager.updateVisibleTiles();
          }
        });
        
      } else {
        // 使用普通的全景加载方式
        const texture = await textureLoaderHandle(url);
        const material = new MeshBasicMaterial({
          map: texture,
          side: DoubleSide
        });
        
        if (this.sphere) {
          this.sphere.material = material;
          console.log("🚀 ~ file: fullView.js ~ initContent ~ material", material);
        } else {
          const sphereGeometry = new SphereGeometry(1, 50, 50);
          // 贴图内翻
          sphereGeometry.scale(1, 1, -1);
          this.sphere = new Mesh(sphereGeometry, material);
          this.scene.add(this.sphere);
        }
      }
    } catch (error) {
      console.error("初始化全景内容失败:", error);
      alert(`加载全景失败: ${error.message}\n请确保分片文件路径正确并含有所有必要的瓦片文件。`);
    }
  }
  
  /**
   * 使用文件对象创建全景图
   * @param {File} file - 全景图文件
   * @param {boolean} useTiled - 是否使用分片加载
   * @returns {Promise<void>}
   */
  async createPanoFromFile(file, useTiled = true) {
    try {
      console.log(`开始处理全景图文件: ${file.name}`);
      
      // 判断是否已在本地存储中
      let panoData;
      const panoName = file.name.replace(/\.[^/.]+$/, ""); // 移除扩展名
      
      try {
        // 尝试从存储中获取
        panoData = await this.panoSlicer.getFromStorage(panoName);
        console.log(`从本地存储中加载全景图: ${panoName}`);
      } catch (e) {
        console.log(`本地存储中不存在该全景图，开始处理: ${panoName}`);
        
        // 根据用户选择决定切片方式
        if (useTiled) {
          // 生成多分辨率瓦片
          panoData = await this.panoSlicer.makeTiles(file);
        } else {
          // 生成立方体六个面
          panoData = await this.panoSlicer.makeCube(file);
        }
        
        // 保存到本地存储
        await this.panoSlicer.saveToStorage(panoName, panoData);
        console.log(`全景图已保存到本地存储: ${panoName}`);
      }
      
      // 创建一个Blob URL用于加载
      const blob = panoData.content;
      const blobUrl = URL.createObjectURL(blob);
      
      // 加载全景图
      await this.initContent(blobUrl, true);
      
      return panoData;
    } catch (error) {
      console.error("处理全景图文件失败:", error);
      alert(`全景图处理失败: ${error.message}`);
      throw error;
    }
  }
  
  resizeHandle() {
    const width = this.container.clientWidth;
    const height = this.container.clientHeight;
    this.renderer?.setSize(width, height);
    //窗口宽高比
    if(this.camera) {
      this.camera.aspect = width / height;
      this.camera.updateProjectionMatrix();
    }
    
    // 如果使用分片加载，更新可见瓦片
    if (this.useTiledLoading && this.tiledPanoramaManager) {
      this.tiledPanoramaManager.updateVisibleTiles();
    }
  }
  
  render() {
    this.camera.updateProjectionMatrix();
    this.renderer.render(this.scene, this.camera);
  }
  
  /**
   * 切换分片加载模式
   * @param {boolean} useTiledLoading - 是否使用分片加载
   */
  async toggleTiledLoading(useTiledLoading) {
    if (this.useTiledLoading !== useTiledLoading) {
      await this.initContent(this.contentTextureUrl, useTiledLoading);
    }
  }
  
  /**
   * 设置是否使用KRPano格式
   * @param {boolean} useKrpano - 是否使用KRPano格式
   */
  setUseKrpanoFormat(useKrpano) {
    if (this.useKrpanoFormat !== useKrpano) {
      this.useKrpanoFormat = useKrpano;
      // 如果当前已加载全景图，需要重新加载
      if (this.contentTextureUrl && this.useTiledLoading) {
        this.initContent(this.contentTextureUrl, true);
      }
    }
  }
  
  /**
   * 释放资源
   */
  dispose() {
    if (this.tiledPanoramaManager) {
      this.tiledPanoramaManager.dispose();
    }
    
    if (this.sphere) {
      this.scene.remove(this.sphere);
      if (this.sphere.material.map) {
        this.sphere.material.map.dispose();
      }
      this.sphere.material.dispose();
      this.sphere.geometry.dispose();
    }
    
    this.renderer.dispose();
    this.controls.dispose();
  }
}