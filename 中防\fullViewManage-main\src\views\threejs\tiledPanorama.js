import {
  TextureLoader,
  SphereGeometry,
  MeshBasicMaterial,
  Mesh,
  Group,
  LinearFilter,
  BoxGeometry,
  EdgesGeometry,
  LineBasicMaterial,
  LineSegments,
  DoubleSide,
  BackSide,
  CanvasTexture,
  PlaneGeometry,
  Vector3,
  Vector2,
  MathUtils,
  ClampToEdgeWrapping
} from 'three';
import KrpanoToolJS from '@krpano/js-tools'

/**
 * 全景图加载管理器
 * 支持六面立方体加载方式
 */
export class TiledPanoramaManager {
  constructor(scene, camera, options = {}) {
    this.scene = scene;
    this.camera = camera;

    // 默认选项 - 按照KRPano的标准设置
    this.options = Object.assign({
      basePath: '', // 全景图所在基础路径
      fileExtension: '.jpg', // 文件扩展名
      previewFile: 'preview.jpg', // 低分辨率预览图
      maxLevel: 3, // 最大层级数（L1, L2, L3）
      tileSize: 512, // 每个瓦片的尺寸
      throttleTime: 300, // 节流时间（毫秒）- 降低以提高响应性
      fovThresholds: [70, 40], // FOV阈值，从大到小排序 [L1/L2边界, L2/L3边界]
      preloadNextLevel: true, // 是否预加载下一层级
      tileResolution: 512, // 瓦片分辨率
      maxTiles: 64, // 最大同时显示的瓦片数 - 增加以提高质量
      useKrpanoFormat: true, // 是否使用KRPano格式
      memoryLimit: 256, // 内存限制（MB）
      adaptiveFov: true, // 是否根据FOV自适应加载层级
      loadingPriority: 'visible', // 加载优先级：visible-可见面优先, all-所有面
      qualityMode: 'balanced', // 质量模式：high-高质量, balanced-平衡, performance-性能优先
    }, options);

    // 根据质量模式调整参数
    if (this.options.qualityMode === 'high') {
      this.options.maxTiles = Math.max(this.options.maxTiles, 96);
      this.options.memoryLimit = Math.max(this.options.memoryLimit, 384);
    } else if (this.options.qualityMode === 'performance') {
      this.options.maxTiles = Math.min(this.options.maxTiles, 48);
      this.options.memoryLimit = Math.min(this.options.memoryLimit, 192);
      this.options.throttleTime = Math.max(this.options.throttleTime, 400);
    }

    // 初始化加载器和缓存
    this.textureLoader = new TextureLoader();
    this.panoramaGroup = new Group();
    this.previewLoaded = false;
    this.currentLevel = 1; // 当前层级
    this.tileMeshes = new Map(); // 瓦片网格缓存
    this.faceGroups = new Map(); // 每个面的组
    this.faceTextures = new Map(); // 每个面的完整纹理
    this.imageCache = new Map(); // 图片缓存
    this.lastUpdateTime = 0; // 上次更新时间
    this.pendingUpdate = false; // 是否有待处理的更新
    this.loadingLevel = null; // 当前正在加载的层级
    this.preloadedLevels = new Set(); // 已预加载的层级
    this.visibleTiles = new Set(); // 当前可见的瓦片
    this.memoryUsage = 0; // 当前内存使用量（MB）

    // 初始化KRPano工具
    this.krpanoTool = new KrpanoToolJS();

    // 初始化六个面的组
    this.initFaceGroups();

    console.log('TiledPanoramaManager 初始化完成，使用KRPano加载逻辑');
    console.log('配置:', {
      maxLevel: this.options.maxLevel,
      fovThresholds: this.options.fovThresholds,
      maxTiles: this.options.maxTiles,
      qualityMode: this.options.qualityMode
    });
  }

  /**
   * 初始化六个面的组
   */
  initFaceGroups() {
    const faces = ['f', 'b', 'l', 'r', 'u', 'd'];
    faces.forEach(face => {
      const group = new Group();
      this.faceGroups.set(face, group);
      this.panoramaGroup.add(group);
    });
  }

  /**
   * 获取当前视野内可见的瓦片
   * @param {string} face - 面
   * @param {number} level - 层级
   * @returns {Array<{row: number, col: number}>} - 可见瓦片列表
   */
  async getVisibleTiles(face, level) {
    const visibleTiles = [];
    try {
        // 尝试获取文件夹数量
        const folderCount = await this.getFolderCount(face, level);
        const tilesPerRow = Math.pow(2, level - 1); // 每行的瓦片数量

        for (let folder = 0; folder < folderCount; folder++) {
            for (let col = 0; col < tilesPerRow; col++) {
                visibleTiles.push({ row: folder, col: col });
            }
        }
    } catch (error) {
        console.warn(`无法获取${face}面l${level}层级的文件夹数量，使用默认值3`);
        // 如果获取失败，使用默认值3
        const tilesPerRow = Math.pow(2, level - 1);
        for (let folder = 0; folder < 3; folder++) {
            for (let col = 0; col < tilesPerRow; col++) {
                visibleTiles.push({ row: folder, col: col });
            }
        }
    }
    return visibleTiles;
  }

  /**
   * 获取面的中心点
   * @param {string} face - 面
   * @returns {Vector3} - 中心点
   */
  getFaceCenter(face) {
    const center = new Vector3();
    switch (face) {
      case 'f': center.set(0, 0, -1); break;
      case 'b': center.set(0, 0, 1); break;
      case 'l': center.set(-1, 0, 0); break;
      case 'r': center.set(1, 0, 0); break;
      case 'u': center.set(0, 1, 0); break;
      case 'd': center.set(0, -1, 0); break;
    }
    return center;
  }

  /**
   * 检测文件夹结构并获取有效的瓦片信息
   * @param {string} face - 面
   * @param {number} level - 层级
   * @returns {Promise<Array>} - 有效瓦片列表 [{row, col, url}]
   */
  async detectTileStructure(face, level) {
    const validTiles = [];
    const tilesPerRow = Math.pow(2, level - 1);

    // 最大可能的文件夹数量（通常不会超过这个数）
    const maxFolders = level === 1 ? 3 : 6;

    console.log(`检测${face}面l${level}层级的文件夹结构，最大可能文件夹数：${maxFolders}`);

    // 并行检查所有可能的文件夹和文件
    const checkPromises = [];

    for (let row = 1; row <= maxFolders; row++) {
      for (let col = 1; col <= tilesPerRow; col++) {
        const url = `${this.options.basePath}/${face}/l${level}/${row}/l${level}_${face}_${row}_${col}${this.options.fileExtension}`;

        const checkPromise = new Promise(resolve => {
          const img = new Image();
          img.onload = () => {
            validTiles.push({ row, col, url, width: img.width, height: img.height });
            console.log(`检测到有效瓦片: ${url}`);
            resolve();
          };
          img.onerror = () => {
            // 文件不存在，忽略
            resolve();
          };
          img.src = url;
        });

        checkPromises.push(checkPromise);
      }
    }

    // 等待所有检查完成
    await Promise.all(checkPromises);

    console.log(`${face}面l${level}层级检测完成，有效瓦片数：${validTiles.length}`);
    return validTiles;
  }

  /**
   * 获取一面所有切片的实际宽高和3D映射信息
   */
  async getTileInfos(face, level) {
    // 检测有效的瓦片结构
    const validTiles = await this.detectTileStructure(face, level);

    if (validTiles.length === 0) {
      console.warn(`${face}面l${level}层级没有有效瓦片，使用默认值`);
      // 使用默认值
      const tilesPerRow = Math.pow(2, level - 1);
      const defaultTiles = [];
      const defaultSize = this.options.tileSize || 512;

      for (let row = 1; row <= 3; row++) {
        for (let col = 1; col <= tilesPerRow; col++) {
          defaultTiles.push({
            row, col,
            width: defaultSize,
            height: defaultSize,
            url: `${this.options.basePath}/${face}/l${level}/${row}/l${level}_${face}_${row}_${col}${this.options.fileExtension}`
          });
        }
      }

      // 计算默认的总宽高
      const rowHeights = Array(3).fill(defaultSize);
      const colWidths = Array(tilesPerRow).fill(defaultSize);
      const totalWidth = defaultSize * tilesPerRow;
      const totalHeight = defaultSize * 3;

      return {
        tileInfos: defaultTiles,
        totalWidth,
        totalHeight,
        rowHeights,
        colWidths,
        tilesPerRow,
        tilesPerCol: 3
      };
    }

    // 分析有效瓦片，确定行列结构
    const rows = [...new Set(validTiles.map(t => t.row))].sort((a, b) => a - b);
    const cols = [...new Set(validTiles.map(t => t.col))].sort((a, b) => a - b);
    const tilesPerCol = rows.length;
    const tilesPerRow = cols.length;

    // 计算每行高度和每列宽度
    const rowHeights = Array(tilesPerCol).fill(0);
    const colWidths = Array(tilesPerRow).fill(0);

    // 填充行高和列宽
    validTiles.forEach(tile => {
      const rowIndex = rows.indexOf(tile.row);
      const colIndex = cols.indexOf(tile.col);

      if (rowIndex === 0) colWidths[colIndex] = tile.width;
      if (colIndex === 0) rowHeights[rowIndex] = tile.height;
    });

    // 计算总宽高
    const totalWidth = colWidths.reduce((a, b) => a + b, 0);
    const totalHeight = rowHeights.reduce((a, b) => a + b, 0);

    console.log(`完成加载${face}面l${level}层级的瓦片信息，总瓦片数:${validTiles.length}，总宽度:${totalWidth}，总高度:${totalHeight}`);

    return {
      tileInfos: validTiles,
      totalWidth,
      totalHeight,
      rowHeights,
      colWidths,
      tilesPerRow,
      tilesPerCol
    };
  }

  /**
   * 创建一面所有切片的Mesh并添加到对应Group，实现无缝拼接
   */
  async createFaceTiles(face, level) {
    // L1加载后删除预览图
    if (level === 1 && this.previewCube) {
      this.panoramaGroup.remove(this.previewCube);
      this.previewCube.geometry.dispose();
      if (Array.isArray(this.previewCube.material)) {
        this.previewCube.material.forEach(m => {
          if (m.map) m.map.dispose();
          m.dispose();
        });
      } else {
        if (this.previewCube.material.map) this.previewCube.material.map.dispose();
        this.previewCube.material.dispose();
      }
      this.previewCube = null;
    }

    const group = this.faceGroups.get(face);
    // 清空原有内容
    while (group.children.length > 0) {
      const mesh = group.children[0];
      if (mesh.material.map) mesh.material.map.dispose();
      mesh.material.dispose();
      mesh.geometry.dispose();
      group.remove(mesh);
    }
    // 获取切片信息
    const { tileInfos, totalWidth, totalHeight, rowHeights, tilesPerRow, tilesPerCol } = await this.getTileInfos(face, level);
    // 按行列排序瓦片信息
    const sortedTileInfos = tileInfos.sort((a, b) => {
      if (a.row === b.row) {
        return a.col - b.col;
      }
      return a.row - b.row;
    });

    // 逐行逐列累加偏移，精确定位
    let yOffset = 1;
    const faceMap = {
      f: {
        pos: (mesh, x, y) => mesh.position.set(x, y, -1),
        flipX: true,
        flipY: false
      },
      b: {
        pos: (mesh, x, y) => mesh.position.set(-x, y, 1),
        flipX: true,
        flipY: false
      },
      l: {
        pos: (mesh, x, y) => mesh.position.set(-1, y, -x),
        flipX: false,
        flipY: false
      },
      r: {
        pos: (mesh, x, y) => mesh.position.set(1, y, x),
        flipX: false,
        flipY: false
      },
      u: {
        pos: (mesh, x, y) => mesh.position.set(x, 1, y),
        flipX: false,
        flipY: true
      },
      d: {
        pos: (mesh, x, y) => mesh.position.set(x, -1, -y),
        flipX: false,
        flipY: true
      }
    };

    for (let row = 0; row < tilesPerCol; row++) {
      let xOffset = -1;
      const rowTiles = sortedTileInfos.filter(t => t.row === row + 1);

      for (let col = 0; col < tilesPerRow; col++) {
        const info = rowTiles[col];
        if (!info) {
          console.warn(`未找到瓦片信息: face=${face}, row=${row + 1}, col=${col + 1}`);
          continue;
        }
        let width3d = 0;
        try {
          width3d = (info.width / totalWidth) * 2;
          const height3d = (info.height / totalHeight) * 2;
          const x = xOffset + width3d / 2;
          const y = yOffset - height3d / 2;

          const geometry = new PlaneGeometry(width3d, height3d);
          const texture = await this.loadTextureWithRetry(info.url);
          texture.wrapS = texture.wrapT = ClampToEdgeWrapping;
          texture.minFilter = texture.magFilter = LinearFilter;

          const material = new MeshBasicMaterial({
            map: texture,
            transparent: true,
            opacity: 1.0,
            side: DoubleSide
          });

          const mesh = new Mesh(geometry, material);
          const map = faceMap[face];

          // 根据面的方向设置纹理翻转
          if (map.flipX) {
            texture.repeat.x = -1;
            texture.offset.x = 1;
          }
          if (map.flipY) {
            texture.repeat.y = -1;
            texture.offset.y = 1;
          }

          // 应用位置变换
          map.pos(mesh, x, y);

          // 应用旋转
          const rotation = this.getTileRotation(face);
          mesh.rotation.set(rotation.x, rotation.y, rotation.z);
          mesh.renderOrder = 1;

          group.add(mesh);
          console.log(`成功创建瓦片: face=${face}, row=${row + 1}, col=${col + 1}, position=(${mesh.position.x}, ${mesh.position.y}, ${mesh.position.z}), width3d=${width3d}`);
        } catch (error) {
          console.error(`创建瓦片失败: face=${face}, row=${row + 1}, col=${col + 1}`, error);
          width3d = (this.options.tileSize || 512) / totalWidth * 2;
        }
        xOffset += width3d;
      }
      yOffset -= (rowHeights[row] / totalHeight) * 2;
    }
  }

  /**
   * 更新可见瓦片 - 按照KRPano的加载逻辑
   * 根据当前FOV和视角动态加载可见瓦片
   */
  async updateVisibleTiles() {
    // 如果没有相机或场景，直接返回
    if (!this.camera || !this.scene) return;

    // 防止频繁更新，使用节流
    const now = Date.now();
    if (now - this.lastUpdateTime < this.options.throttleTime) {
      if (!this.pendingUpdate) {
        this.pendingUpdate = true;
        setTimeout(() => {
          this.pendingUpdate = false;
          this.updateVisibleTiles();
        }, this.options.throttleTime);
      }
      return;
    }
    this.lastUpdateTime = now;

    // 确定当前应该使用的层级
    const fov = this.camera.fov;
    let targetLevel = 1; // 默认使用L1层级

    // 根据FOV选择合适的层级
    // FOV越小（放大），使用越高的层级
    if (fov <= this.options.fovThresholds[1]) {
      // 如果FOV很小，使用最高层级
      targetLevel = this.options.maxLevel;
    } else if (fov <= this.options.fovThresholds[0]) {
      // 中等FOV，使用中间层级
      targetLevel = 2;
    }

    // 如果层级发生变化，记录当前层级
    if (targetLevel !== this.currentLevel) {
      console.log(`FOV: ${fov}，切换到层级 L${targetLevel}`);
      this.currentLevel = targetLevel;
    }

    // 获取当前视角可见的面
    const visibleFaces = this.getVisibleFaces();

    // 首次加载时，确保至少加载L1层级的所有面
    if (!this._tilesLoaded) {
      try {
        console.log('首次加载全景图层级...');

        // 先加载L1层级的所有面
        const faces = ['f', 'b', 'l', 'r', 'u', 'd'];
        for (const face of faces) {
          await this.createFaceTiles(face, 1);
        }

        this._tilesLoaded = true;
        console.log('L1层级加载完成');

        // 如果目标层级大于1，预加载L2层级的可见面
        if (targetLevel > 1) {
          for (const face of visibleFaces) {
            await this.createFaceTiles(face, targetLevel);
          }
          console.log(`L${targetLevel}层级可见面加载完成`);
        }
      } catch (error) {
        console.error('加载全景图层级失败:', error);
        this._tilesLoaded = true;
      }
      return;
    }

    // 非首次加载，根据当前视角和层级加载可见瓦片
    try {
      // 加载当前层级的可见面
      for (const face of visibleFaces) {
        await this.createFaceTiles(face, targetLevel);
      }

      // 预加载下一层级（如果有且设置了预加载）
      if (this.options.preloadNextLevel && targetLevel < this.options.maxLevel) {
        const nextLevel = targetLevel + 1;
        if (!this.preloadedLevels.has(nextLevel)) {
          await this.preloadLevel(nextLevel);
        }
      }

      // 清理不可见的瓦片，释放内存
      this.cleanupInvisibleTiles();

    } catch (error) {
      console.error('更新可见瓦片失败:', error);
    }
  }

  /**
   * 获取当前视角可见的面
   * @returns {Array<string>} - 可见面的数组 ['f', 'r', 'u', ...]
   */
  getVisibleFaces() {
    // 获取相机方向
    const direction = new Vector3(0, 0, -1);
    direction.applyQuaternion(this.camera.quaternion);

    // 计算相机朝向的角度
    const phi = Math.atan2(direction.z, direction.x);
    const theta = Math.acos(direction.y / direction.length());

    // 确定主要可见面
    const visibleFaces = [];

    // 前后面判断
    if (Math.abs(phi) < Math.PI/4) {
      visibleFaces.push('f'); // 前
    } else if (Math.abs(phi) > 3*Math.PI/4) {
      visibleFaces.push('b'); // 后
    }

    // 左右面判断
    if (phi > Math.PI/4 && phi < 3*Math.PI/4) {
      visibleFaces.push('l'); // 左
    } else if (phi < -Math.PI/4 && phi > -3*Math.PI/4) {
      visibleFaces.push('r'); // 右
    }

    // 上下面判断
    if (theta < Math.PI/4) {
      visibleFaces.push('u'); // 上
    } else if (theta > 3*Math.PI/4) {
      visibleFaces.push('d'); // 下
    }

    // 如果没有确定任何面，默认加载所有面
    if (visibleFaces.length === 0) {
      return ['f', 'b', 'l', 'r', 'u', 'd'];
    }

    return visibleFaces;
  }

  /**
   * 清理不可见的瓦片
   * 按照KRPano的逻辑，优化内存使用
   */
  cleanupInvisibleTiles() {
    // 获取当前视角可见的面
    const visibleFaces = this.getVisibleFaces();
    const currentLevel = this.currentLevel;
    const maxTiles = this.options.maxTiles;

    // 如果瓦片数量未超过限制，不进行清理
    if (this.tileMeshes.size <= maxTiles) {
      return;
    }

    // 标记当前所有瓦片的状态
    const tileStatus = new Map();
    for (const [key, mesh] of this.tileMeshes.entries()) {
      const [face, level] = key.split('_');
      const levelNum = parseInt(level.substring(1));

      // 计算瓦片的优先级
      let priority = 0;

      // 当前层级的可见面有最高优先级
      if (levelNum === currentLevel && visibleFaces.includes(face)) {
        priority = 3;
      }
      // 当前层级的不可见面次之
      else if (levelNum === currentLevel) {
        priority = 2;
      }
      // 其他层级的可见面再次之
      else if (visibleFaces.includes(face)) {
        priority = 1;
      }
      // 其他层级的不可见面优先级最低
      else {
        priority = 0;
      }

      tileStatus.set(key, {
        mesh,
        priority,
        lastUsed: mesh.lastVisibleTime || 0
      });
    }

    // 按优先级和最后使用时间排序
    const sortedTiles = Array.from(tileStatus.entries())
      .sort((a, b) => {
        // 先按优先级排序（从低到高）
        if (a[1].priority !== b[1].priority) {
          return a[1].priority - b[1].priority;
        }
        // 优先级相同时，按最后使用时间排序（从早到晚）
        return a[1].lastUsed - b[1].lastUsed;
      });

    // 计算需要移除的瓦片数量
    const removeCount = Math.max(0, this.tileMeshes.size - maxTiles);

    // 移除优先级最低的瓦片
    const tilesToRemove = sortedTiles.slice(0, removeCount);
    for (const [key, { mesh }] of tilesToRemove) {
      const face = key.split('_')[0];
      const group = this.faceGroups.get(face);
      if (group) {
        group.remove(mesh);
      }
      if (mesh.material.map) {
        mesh.material.map.dispose();
      }
      mesh.material.dispose();
      mesh.geometry.dispose();
      this.tileMeshes.delete(key);

      console.log(`释放瓦片: ${key}`);
    }

    // 如果移除了瓦片，输出当前内存使用情况
    if (tilesToRemove.length > 0) {
      console.log(`清理了 ${tilesToRemove.length} 个瓦片，当前瓦片数: ${this.tileMeshes.size}`);
    }
  }

  /**
   * 检查指定层级是否存在
   * @param {number} level - 要检查的层级
   * @returns {Promise<boolean>} - 层级是否存在
   */
  async checkLevelExists(level) {
    return new Promise((resolve) => {
      // 检查第一个面（f）的第一张图片是否存在
      const imageUrl = `${this.options.basePath}/f/l${level}/1/l${level}_f_1_1${this.options.fileExtension}`;
      const img = new Image();

      img.onload = () => {
        console.log(`层级 ${level} 存在，找到图片: ${imageUrl}`);
        resolve(true);
      };

      img.onerror = () => {
        console.log(`层级 ${level} 不存在或无法访问图片: ${imageUrl}`);
        resolve(false);
      };

      img.src = imageUrl;
    });
  }

  /**
   * 预加载指定层级
   * 按照KRPano的逻辑，智能预加载可能需要的瓦片
   * @param {number} level - 要预加载的层级
   */
  async preloadLevel(level) {
    if (this.preloadedLevels.has(level)) {
      return;
    }

    // 检查层级是否存在
    const levelExists = await this.checkLevelExists(level);
    if (!levelExists) {
      console.log(`层级 ${level} 不存在，跳过预加载`);
      return;
    }

    console.log(`开始预加载层级 ${level}`);

    try {
      // 获取当前视角可见的面
      const visibleFaces = this.getVisibleFaces();

      // 只预加载可见面的第一个瓦片，减少初始加载时间
      for (const face of visibleFaces) {
        const imageUrl = `${this.options.basePath}/${face}/l${level}/1/l${level}_${face}_1_1${this.options.fileExtension}`;
        await this.loadImage(imageUrl);
        console.log(`预加载 ${face} 面 L${level} 层级的第一个瓦片`);
      }

      // 标记该层级已预加载
      this.preloadedLevels.add(level);
      console.log(`层级 ${level} 预加载完成`);

      // 如果当前FOV很小（用户放大了视图），可能很快就需要更高层级
      // 在后台继续加载更多瓦片
      if (this.camera.fov < this.options.fovThresholds[1]) {
        setTimeout(() => {
          this.preloadMoreTiles(level, visibleFaces);
        }, 500);
      }
    } catch (error) {
      console.log(`层级 ${level} 预加载失败:`, error);
    }
  }

  /**
   * 在后台预加载更多瓦片
   * @param {number} level - 要预加载的层级
   * @param {Array<string>} faces - 要预加载的面
   */
  async preloadMoreTiles(level, faces) {
    try {
      for (const face of faces) {
        // 预加载第一行的所有瓦片
        const tilesPerRow = Math.pow(2, level - 1);
        for (let col = 2; col <= tilesPerRow; col++) {
          const imageUrl = `${this.options.basePath}/${face}/l${level}/1/l${level}_${face}_1_${col}${this.options.fileExtension}`;
          this.loadImage(imageUrl).catch(() => {
            // 忽略加载错误，这只是预加载
          });
        }

        // 预加载第二行的第一个瓦片
        if (level > 1) {
          const imageUrl = `${this.options.basePath}/${face}/l${level}/2/l${level}_${face}_2_1${this.options.fileExtension}`;
          this.loadImage(imageUrl).catch(() => {
            // 忽略加载错误，这只是预加载
          });
        }
      }
    } catch (error) {
      // 忽略错误，这只是后台预加载
    }
  }

  /**
   * 加载图片并缓存
   * @param {string} url - 图片URL
   * @returns {Promise<HTMLImageElement>} - 图片元素
   */
  loadImage(url) {
    // 检查缓存
    if (this.imageCache.has(url)) {
      return Promise.resolve(this.imageCache.get(url));
    }

    return new Promise((resolve, reject) => {
      const img = new Image();
      img.crossOrigin = 'Anonymous';

      img.onload = () => {
        console.log(`图片加载成功: ${url}`);
        // 将图片存入缓存
        this.imageCache.set(url, img);
        resolve(img);
      };

      img.onerror = (error) => {
        console.error(`图片加载失败: ${url}`, error);
        reject(error);
      };

      img.src = url;
    });
  }

  /**
   * 使用重试功能加载纹理
   * @param {string} url - 纹理URL
   * @param {number} maxRetries - 最大重试次数
   * @returns {Promise<Texture>} - 纹理对象
   */
  loadTextureWithRetry(url, maxRetries = 2) {
    return new Promise((resolve, reject) => {
      const attemptLoad = (retriesLeft) => {
        this.textureLoader.load(
          url,
          resolve,
          undefined,
          (error) => {
            if (retriesLeft > 0) {
              console.log(`加载失败，重试中... (${maxRetries - retriesLeft + 1}/${maxRetries}): ${url}`);
              setTimeout(() => attemptLoad(retriesLeft - 1), 500);
            } else {
              reject(error);
            }
          }
        );
      };

      attemptLoad(maxRetries);
    });
  }

  /**
   * 清理全景图资源
   */
  dispose() {
    // 清理瓦片网格
    for (const [_, mesh] of this.tileMeshes) {
      if (mesh.material.map) {
        mesh.material.map.dispose();
      }
      mesh.material.dispose();
      mesh.geometry.dispose();
    }
    this.tileMeshes.clear();

    // 清理面的组
    for (const group of this.faceGroups.values()) {
      while (group.children.length > 0) {
        const mesh = group.children[0];
      if (mesh.material.map) {
        mesh.material.map.dispose();
      }
      mesh.material.dispose();
      mesh.geometry.dispose();
        group.remove(mesh);
      }
    }

    // 清理图片缓存
    this.imageCache.clear();
    this.preloadedLevels.clear();

    // 从场景中移除全景组
    if (this.scene.children.includes(this.panoramaGroup)) {
      this.scene.remove(this.panoramaGroup);
    }

    // 重置状态
    this.previewLoaded = false;
    this.currentLevel = 1;
    this.loadingLevel = null;
  }

  /**
   * 初始化全景图
   * 按照KRPano的加载逻辑优化加载过程
   * @param {string} panoramaPath - 全景图路径
   * @param {boolean} showDebug - 是否显示调试信息
   */
  async initPanorama(panoramaPath, showDebug = false) {
    console.log(`初始化全景图: ${panoramaPath}`);
    this.currentPanoramaPath = panoramaPath;

    // 确保basePath末尾没有斜杠
    const base = panoramaPath.endsWith('/')
      ? panoramaPath.slice(0, -1)
      : panoramaPath;

    // 设置基础路径
    this.options.basePath = base;

    // 重置状态
    this._tilesLoaded = false;
    this.currentLevel = 1;
    this.preloadedLevels.clear();
    this.lastUpdateTime = 0;
    this.pendingUpdate = false;
    this.memoryUsage = 0;

    // 清理现有瓦片
    for (const [key, mesh] of this.tileMeshes.entries()) {
      const face = key.split('_')[0];
      const group = this.faceGroups.get(face);
      if (group) {
        group.remove(mesh);
      }
      if (mesh.material.map) {
        mesh.material.map.dispose();
      }
      mesh.material.dispose();
      mesh.geometry.dispose();
    }
    this.tileMeshes.clear();
    this.imageCache.clear();

    // 将全景组添加到场景（如果尚未添加）
    if (!this.scene.children.includes(this.panoramaGroup)) {
      this.scene.add(this.panoramaGroup);
    }

    // 首先加载预览图 - 提供快速的初始视图
    try {
      console.log('加载预览图...');
      await this.loadPreview();
      console.log('预览图加载完成');
    } catch (error) {
      console.warn('预览图加载失败，继续加载瓦片:', error);
    }

    // 检测可用的最大层级
    let maxAvailableLevel = 1;
    for (let level = 1; level <= this.options.maxLevel; level++) {
      const exists = await this.checkLevelExists(level);
      if (exists) {
        maxAvailableLevel = level;
      } else {
        break;
      }
    }

    // 更新最大可用层级
    this.options.maxLevel = maxAvailableLevel;
    console.log(`检测到最大可用层级: L${maxAvailableLevel}`);

    // 根据当前FOV确定初始加载层级
    const initialLevel = this.camera.fov <= this.options.fovThresholds[1] ?
                         Math.min(3, maxAvailableLevel) :
                         (this.camera.fov <= this.options.fovThresholds[0] ?
                          Math.min(2, maxAvailableLevel) : 1);

    console.log(`初始加载层级: L${initialLevel}，当前FOV: ${this.camera.fov}`);

    // 加载初始层级的可见瓦片
    await this.updateVisibleTiles();

    // 显示调试信息
    if (showDebug) {
      setTimeout(() => {
        this.showCubeFaceStructure();
      }, 1000);
    }

    console.log('全景图初始化完成');
    return true;
  }

  /**
   * 加载预览图（1x6竖排大图）
   */
  async loadPreview() {
    try {
      const base = this.options.basePath;
      const ext = this.options.fileExtension;
      const previewUrl = `${base}/preview${ext}`;
      // 加载1x6竖排大图
      const img = await this.loadImage(previewUrl);
      // 你的图片顺序：前、右、后、左、上、下
      // three.js BoxGeometry顺序：右、左、上、下、前、后
      // 0:前(f) 1:右(r) 2:后(b) 3:左(l) 4:上(u) 5:下(d)
      // BoxGeometry:    r   l   u   d   f   b
      //                1   3   4   5   0   2
      const boxOrder = [1, 3, 4, 5, 0, 2];
      const faceWidth = img.width;
      const faceHeight = img.height / 6;
      const materials = [];
      for (let i = 0; i < 6; i++) {
        const srcIdx = boxOrder[i];
        const canvas = document.createElement('canvas');
        // 上下两面需要旋转
        if (i === 2 || i === 3) {
          canvas.width = faceHeight;
          canvas.height = faceWidth;
        } else {
          canvas.width = faceWidth;
          canvas.height = faceHeight;
        }
        const ctx = canvas.getContext('2d');
        if (i === 2) { // 上面（u），逆时针旋转90°
          ctx.save();
          ctx.translate(0, faceWidth);
          ctx.rotate(-Math.PI / 2);
          ctx.drawImage(img, 0, srcIdx * faceHeight, faceWidth, faceHeight, 0, 0, faceWidth, faceHeight);
          ctx.restore();
        } else if (i === 3) { // 下面（d），顺时针旋转90°
          ctx.save();
          ctx.translate(faceHeight, 0);
          ctx.rotate(Math.PI / 2);
          ctx.drawImage(img, 0, srcIdx * faceHeight, faceWidth, faceHeight, 0, 0, faceWidth, faceHeight);
          ctx.restore();
        } else {
          ctx.drawImage(img, 0, srcIdx * faceHeight, faceWidth, faceHeight, 0, 0, faceWidth, faceHeight);
        }
        const texture = new CanvasTexture(canvas);
        texture.wrapS = texture.wrapT = ClampToEdgeWrapping;
        texture.minFilter = texture.magFilter = LinearFilter;
        materials[i] = new MeshBasicMaterial({
          map: texture,
          transparent: true,
          opacity: 1.0,
          side: BackSide // 贴图朝内
        });
            }
      // 创建立方体网格
      const cubeGeometry = new BoxGeometry(2, 2, 2);
      this.previewCube = new Mesh(cubeGeometry, materials);
      this.panoramaGroup.add(this.previewCube);
      this.previewLoaded = true;
      return true;
    } catch (error) {
      console.error('1x6竖排预览图加载失败:', error);
      this.previewLoaded = false;
      return false;
    }
  }

  /**
   * 显示六面立方体结构（调试用）
   */
  showCubeFaceStructure() {
    // 创建立方体线框，用于显示六个面的边界
    const cubeSize = 1.001; // 略大于球体半径
    const cubeGeometry = new BoxGeometry(cubeSize * 2, cubeSize * 2, cubeSize * 2);
    const edgesGeometry = new EdgesGeometry(cubeGeometry);
    const lineMaterial = new LineBasicMaterial({
      color: 0xffff00,
      transparent: true,
      opacity: 0.5
    });

    this.debugCube = new LineSegments(edgesGeometry, lineMaterial);
    this.panoramaGroup.add(this.debugCube);

    // 创建每个面的标签
    const labelSize = 0.2;
    const faceLabels = [
      { face: 'f', position: [0, 0, cubeSize], rotation: [0, Math.PI, 0] },
      { face: 'b', position: [0, 0, -cubeSize], rotation: [0, 0, 0] },
      { face: 'l', position: [-cubeSize, 0, 0], rotation: [0, Math.PI/2, 0] },
      { face: 'r', position: [cubeSize, 0, 0], rotation: [0, -Math.PI/2, 0] },
      { face: 'u', position: [0, cubeSize, 0], rotation: [Math.PI/2, 0, 0] },
      { face: 'd', position: [0, -cubeSize, 0], rotation: [-Math.PI/2, 0, 0] }
    ];

    faceLabels.forEach(faceInfo => {
      const { face, position, rotation } = faceInfo;

      // 创建面的中心标记
      const markerGeometry = new PlaneGeometry(labelSize, labelSize);
      const markerMaterial = new MeshBasicMaterial({
        color: 0xff0000,
        transparent: true,
        opacity: 0.7,
        side: DoubleSide
      });

      // 创建标记
      const marker = new Mesh(markerGeometry, markerMaterial);
      marker.position.set(position[0], position[1], position[2]);
      marker.rotation.set(rotation[0], rotation[1], rotation[2]);
      this.panoramaGroup.add(marker);

      console.log(`添加了面 ${face} 的标记`);
    });

    console.log('显示了六面立方体结构');
  }

  async makeCube(file) {
    try {
      console.log('开始生成立方体图...')
      const result = await this.krpanoTool.makeCube(file)
      console.log(`立方体图生成完成，耗时: ${result.duration}ms`)
      return result
    } catch (error) {
      console.error('立方体图生成失败:', error)
      throw error
    }
  }

  async getFolderCount(face, level) {
    return new Promise((resolve, reject) => {
        const checkUrl = `${this.options.basePath}/${face}/l${level}/`;
        const xhr = new XMLHttpRequest();
        xhr.open('HEAD', checkUrl);
        xhr.onload = () => {
            if (xhr.status === 200) {
                // 获取文件夹数量
                const folderCount = parseInt(xhr.getResponseHeader('X-Folder-Count')) || 3;
                resolve(folderCount);
            } else {
                reject(new Error(`无法获取文件夹数量: ${xhr.status}`));
            }
        };
        xhr.onerror = () => reject(new Error('网络错误'));
        xhr.send();
    });
  }

  /**
   * 获取瓦片旋转
   * @param {string} face - 面
   * @returns {Vector3} - 旋转
   */
  getTileRotation(face) {
    switch (face) {
      case 'f': return new Vector3(0, Math.PI, 0);      // 前面
      case 'b': return new Vector3(0, 0, 0);            // 后面
      case 'l': return new Vector3(0, Math.PI / 2, 0);  // 左面
      case 'r': return new Vector3(0, -Math.PI / 2, 0); // 右面
      case 'u': return new Vector3(-Math.PI / 2, 0, 0); // 上面
      case 'd': return new Vector3(Math.PI / 2, 0, 0);  // 下面
      default:  return new Vector3(0, 0, 0);
    }
  }

  /**
   * 获取瓦片的3D信息
   * @param {Array} tileInfos - 瓦片信息数组
   * @param {number} totalWidth - 总宽度
   * @param {number} totalHeight - 总高度
   * @param {Array} rowHeights - 行高数组
   * @param {Array} colWidths - 列宽数组
   * @returns {Array} - 3D信息数组
   */
  getTile3DInfo(tileInfos, totalWidth, totalHeight, rowHeights, colWidths) {
    let result = [];
    let yOffset = 1;
    for (let row = 0; row < rowHeights.length; row++) {
      let xOffset = -1;
      for (let col = 0; col < colWidths.length; col++) {
        const info = tileInfos.find(t => t.row === row + 1 && t.col === col + 1);
        const width3d = (info.width / totalWidth) * 2;
        const height3d = (info.height / totalHeight) * 2;
        const x = xOffset + width3d / 2;
        const y = yOffset - height3d / 2;
        result.push({ ...info, width3d, height3d, x, y });
        xOffset += width3d;
      }
      yOffset -= (rowHeights[row] / totalHeight) * 2;
    }
    return result;
  }
}








