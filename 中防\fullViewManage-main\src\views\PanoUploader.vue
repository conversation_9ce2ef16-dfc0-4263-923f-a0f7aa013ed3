<template>
  <div class="pano-uploader">
    <h2>全景图上传</h2>
    
    <div class="upload-area">
      <input type="file" accept="image/jpeg,image/jpg" @change="onFileSelected" />
      <div class="format-selector">
        <label>
          <input type="radio" v-model="useTiled" :value="true" /> 多分辨率瓦片
        </label>
        <label>
          <input type="radio" v-model="useTiled" :value="false" /> 立方体六面
        </label>
      </div>
      <button @click="uploadFile" :disabled="!selectedFile">上传并处理</button>
    </div>
    
    <div class="existing-pano">
      <button @click="loadExistingPanorama">加载已有全景图</button>
    </div>
    
    <div v-if="processing" class="processing">
      <div class="progress">
        <div class="progress-bar" :style="{width: progress + '%'}"></div>
      </div>
      <div class="status">{{ status }}</div>
    </div>
    
    <div v-if="result" class="result">
      <h3>处理结果</h3>
      <div class="info">
        <p>处理耗时: {{ result.duration }}ms</p>
        <p>目录名称: {{ result.dirName }}</p>
      </div>
      
      <div class="preview">
        <div ref="panoramaContainer" class="panorama-container"></div>
      </div>
    </div>
  </div>
</template>

<script>
import fullView from './threejs/fullView';

export default {
  name: 'PanoUploader',
  data() {
    return {
      selectedFile: null,
      processing: false,
      progress: 0,
      status: '',
      result: null,
      useTiled: true,
      viewer: null
    };
  },
  mounted() {
    // 初始化全景查看器
    this.initViewer();
  },
  beforeDestroy() {
    // 清理资源
    if (this.viewer) {
      this.viewer.dispose();
    }
  },
  methods: {
    initViewer() {
      // 初始化全景查看器
      const container = this.$refs.panoramaContainer;
      if (container) {
        this.viewer = new fullView(container);
        
        // 根据窗口大小调整
        window.addEventListener('resize', this.onResize);
        this.onResize();
      }
    },
    onResize() {
      if (this.viewer) {
        this.viewer.resizeHandle();
      }
    },
    onFileSelected(event) {
      const file = event.target.files[0];
      if (file && (file.type === 'image/jpeg' || file.type === 'image/jpg')) {
        this.selectedFile = file;
      } else {
        alert('请选择JPEG格式的全景图');
        this.selectedFile = null;
        event.target.value = '';
      }
    },
    async uploadFile() {
      if (!this.selectedFile) {
        return;
      }
      
      try {
        this.processing = true;
        this.progress = 0;
        this.status = '开始处理全景图...';
        
        // 模拟进度
        const progressInterval = setInterval(() => {
          if (this.progress < 90) {
            this.progress += 5;
          }
        }, 500);
        
        // 使用viewer处理全景图
        const result = await this.viewer.createPanoFromFile(this.selectedFile, this.useTiled);
        
        // 处理完成
        clearInterval(progressInterval);
        this.progress = 100;
        this.status = '处理完成!';
        this.result = result;
        
        // 短暂延迟后关闭进度条
        setTimeout(() => {
          this.processing = false;
        }, 1000);
      } catch (error) {
        console.error('处理全景图失败:', error);
        this.status = `处理失败: ${error.message}`;
        this.progress = 0;
        
        // 短暂延迟后关闭进度条
        setTimeout(() => {
          this.processing = false;
        }, 3000);
      }
    },
    async loadExistingPanorama() {
      try {
        this.processing = true;
        this.progress = 0;
        this.status = '加载已有全景图...';
        
        // 模拟进度
        const progressInterval = setInterval(() => {
          if (this.progress < 95) {
            this.progress += 10;
          }
        }, 100);
        
        // 基础路径 - 指向public/3d/tiles目录
        const tilesPath = '/3d/tiles';
        
        // 确保使用分片加载模式和KRPano格式
        this.viewer.useTiledLoading = true;
        this.viewer.useKrpanoFormat = true;
        
        // 根据实际文件结构选择初始层级
        // 检查可用层级 l1, l2, l3
        const initialLevel = 2; // 选择l2作为默认层级，提供较好的清晰度和性能
        
        console.log(`加载全景图: ${tilesPath}`);
        console.log(`使用KRPano格式: ${this.viewer.useKrpanoFormat}`);
        console.log(`初始层级: ${initialLevel}，瓦片索引从1开始`);
        
        // 加载全景图
        await this.viewer.initContent(tilesPath, true);
        
        // 处理完成
        clearInterval(progressInterval);
        this.progress = 100;
        this.status = '加载完成!';
        
        // 创建结果对象
        this.result = {
          dirName: tilesPath,
          duration: 0,
          level: initialLevel,
          format: 'KRPano格式 (索引从1开始)'
        };
        
        // 短暂延迟后关闭进度条
        setTimeout(() => {
          this.processing = false;
        }, 500);
      } catch (error) {
        console.error('加载已有全景图失败:', error);
        this.status = `加载失败: ${error.message}`;
        this.progress = 0;
        
        // 短暂延迟后关闭进度条
        setTimeout(() => {
          this.processing = false;
        }, 2000);
      }
    }
  }
};
</script>

<style scoped>
.pano-uploader {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.upload-area {
  margin: 20px 0;
  padding: 20px;
  border: 2px dashed #ccc;
  border-radius: 8px;
  text-align: center;
}

.format-selector {
  margin: 15px 0;
}

.format-selector label {
  margin: 0 10px;
}

.existing-pano {
  margin: 15px 0;
  text-align: center;
}

.existing-pano button {
  background-color: #2196F3;
  margin-bottom: 20px;
}

button {
  background-color: #4CAF50;
  color: white;
  padding: 10px 15px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.processing {
  margin: 20px 0;
}

.progress {
  width: 100%;
  height: 20px;
  background-color: #f3f3f3;
  border-radius: 10px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background-color: #4CAF50;
  transition: width 0.3s ease;
}

.status {
  margin-top: 10px;
  text-align: center;
  font-style: italic;
}

.result {
  margin-top: 30px;
}

.info {
  background-color: #f9f9f9;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.panorama-container {
  width: 100%;
  height: 400px;
  background-color: #000;
  border-radius: 8px;
  overflow: hidden;
}
</style> 