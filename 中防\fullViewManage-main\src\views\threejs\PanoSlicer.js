import KrpanoToolJS from '@krpano/js-tools'

/**
 * 全景图切片工具
 * 基于@krpano/js-tools实现浏览器端切片
 */
export default class PanoSlicer {
  constructor() {
    this.krpanoTool = new KrpanoToolJS()
  }

  /**
   * 生成多分辨率瓦片图
   * @param {File} file - 全景图文件
   * @returns {Promise<Object>} - 切片结果
   */
  async makeTiles(file) {
    try {
      console.log('开始切片全景图...')
      const result = await this.krpanoTool.makeTiles(file)
      console.log(`切片完成，耗时: ${result.duration}ms`)
      return result
    } catch (error) {
      console.error('全景图切片失败:', error)
      throw error
    }
  }

  /**
   * 生成立方体图（6个面）
   * @param {File} file - 全景图文件
   * @returns {Promise<Object>} - 切片结果
   */
  async makeCube(file) {
    try {
      console.log('开始生成立方体图...')
      const result = await this.krpanoTool.makeCube(file)
      console.log(`立方体图生成完成，耗时: ${result.duration}ms`)
      return result
    } catch (error) {
      console.error('立方体图生成失败:', error)
      throw error
    }
  }

  /**
   * 同时生成立方体图和多分辨率瓦片图
   * @param {File} file - 全景图文件
   * @returns {Promise<Object>} - 切片结果
   */
  async makeCubeAndTiles(file) {
    try {
      console.log('开始生成立方体图和多分辨率瓦片图...')
      const result = await this.krpanoTool.makeCubeAndTiles(file)
      console.log(`图片生成完成，耗时: ${result.duration}ms`)
      return result
    } catch (error) {
      console.error('图片生成失败:', error)
      throw error
    }
  }

  /**
   * 将切片结果保存到IndexedDB或LocalStorage中
   * @param {string} name - 全景图名称
   * @param {Object} result - 切片结果
   */
  async saveToStorage(name, result) {
    try {
      // 将Blob转换为Base64字符串
      const reader = new FileReader()
      
      return new Promise((resolve, reject) => {
        reader.onload = () => {
          try {
            const base64Data = reader.result
            // 保存到LocalStorage（注意大小限制）
            localStorage.setItem(`pano_${name}`, JSON.stringify({
              dirName: result.dirName,
              code: result.code,
              timestamp: Date.now()
            }))
            
            // 元数据存LocalStorage，大文件存IndexedDB
            this.saveBlobToIndexedDB(name, result.content)
              .then(() => resolve())
              .catch(reject)
          } catch (error) {
            reject(error)
          }
        }
        reader.onerror = reject
        reader.readAsDataURL(result.content)
      })
    } catch (error) {
      console.error('保存切片结果失败:', error)
      throw error
    }
  }
  
  /**
   * 将Blob保存到IndexedDB
   * @param {string} name - 全景图名称
   * @param {Blob} blob - 切片结果Blob
   */
  async saveBlobToIndexedDB(name, blob) {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('PanoramaDB', 1)
      
      request.onerror = (event) => {
        reject(new Error('无法打开数据库'))
      }
      
      request.onupgradeneeded = (event) => {
        const db = event.target.result
        if (!db.objectStoreNames.contains('panoramas')) {
          db.createObjectStore('panoramas', { keyPath: 'name' })
        }
      }
      
      request.onsuccess = (event) => {
        const db = event.target.result
        const transaction = db.transaction(['panoramas'], 'readwrite')
        const store = transaction.objectStore('panoramas')
        
        const storeRequest = store.put({
          name,
          blob,
          timestamp: Date.now()
        })
        
        storeRequest.onsuccess = () => resolve()
        storeRequest.onerror = () => reject(new Error('保存到IndexedDB失败'))
      }
    })
  }
  
  /**
   * 从IndexedDB获取全景图切片
   * @param {string} name - 全景图名称
   * @returns {Promise<Object>} - 切片结果
   */
  async getFromStorage(name) {
    try {
      // 从LocalStorage获取元数据
      const metaString = localStorage.getItem(`pano_${name}`)
      if (!metaString) {
        throw new Error('全景图元数据不存在')
      }
      
      const metadata = JSON.parse(metaString)
      
      // 从IndexedDB获取Blob
      const blob = await this.getBlobFromIndexedDB(name)
      
      return {
        ...metadata,
        content: blob
      }
    } catch (error) {
      console.error('获取切片结果失败:', error)
      throw error
    }
  }
  
  /**
   * 从IndexedDB获取Blob
   * @param {string} name - 全景图名称
   * @returns {Promise<Blob>} - 切片结果Blob
   */
  async getBlobFromIndexedDB(name) {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('PanoramaDB', 1)
      
      request.onerror = () => {
        reject(new Error('无法打开数据库'))
      }
      
      request.onsuccess = (event) => {
        const db = event.target.result
        const transaction = db.transaction(['panoramas'], 'readonly')
        const store = transaction.objectStore('panoramas')
        
        const getRequest = store.get(name)
        
        getRequest.onsuccess = () => {
          if (getRequest.result) {
            resolve(getRequest.result.blob)
          } else {
            reject(new Error('全景图数据不存在'))
          }
        }
        
        getRequest.onerror = () => reject(new Error('从IndexedDB获取失败'))
      }
    })
  }
} 