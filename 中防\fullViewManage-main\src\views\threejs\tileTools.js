/**
 * 全景图分片工具函数
 * 用于将一个大的全景图切分成多个小分辨率的图块，便于按需加载
 */

/**
 * 获取图像数据
 * @param {string} url - 图像URL
 * @returns {Promise<HTMLImageElement>} - 图像元素
 */
const loadImage = (url) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'Anonymous';
    img.onload = () => resolve(img);
    img.onerror = (err) => reject(err);
    img.src = url;
  });
};

/**
 * 将图像分割成瓦片并下载为ZIP文件
 * @param {string} imageUrl - 全景图URL
 * @param {Object} options - 选项
 * @returns {Promise<Blob>} - 包含所有瓦片的ZIP文件Blob
 */
export async function createPanoramaTiles(imageUrl, options = {}) {
  const { 
    levels = 4, 
    tileSize = 512, 
    format = 'jpg', 
    quality = 0.8, 
    outputPrefix = 'pano_'
  } = options;
  
  try {
    console.log(`开始处理全景图: ${imageUrl}`);
    
    // 加载JSZip库
    let JSZip;
    try {
      JSZip = await import('jszip');
      JSZip = JSZip.default || JSZip;
    } catch (err) {
      console.error('无法加载JSZip库', err);
      throw new Error('无法加载JSZip库，请确保已安装JSZip');
    }
    
    // 创建ZIP文件
    const zip = new JSZip();
    
    // 加载原始图像
    const image = await loadImage(imageUrl);
    console.log(`图像加载成功，尺寸: ${image.width}x${image.height}`);
    
    // 创建Canvas
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    // 创建不同层级的瓦片
    for (let level = 0; level < levels; level++) {
      const tilesPerRow = Math.pow(2, level);
      
      // 计算当前层级的图像尺寸
      const levelWidth = image.width / Math.pow(2, levels - level - 1);
      const levelHeight = image.height / Math.pow(2, levels - level - 1);
      
      console.log(`处理层级 ${level + 1}/${levels}, 尺寸 ${levelWidth}x${levelHeight}, 每行瓦片数 ${tilesPerRow}`);
      
      // 调整Canvas大小以适应当前层级
      canvas.width = levelWidth;
      canvas.height = levelHeight;
      
      // 绘制缩放后的图像
      ctx.drawImage(image, 0, 0, levelWidth, levelHeight);
      
      // 提取瓦片
      const tileWidth = levelWidth / tilesPerRow;
      const tileHeight = levelHeight / tilesPerRow;
      
      // 创建用于瓦片的临时Canvas
      const tileCanvas = document.createElement('canvas');
      tileCanvas.width = tileSize;
      tileCanvas.height = tileSize;
      const tileCtx = tileCanvas.getContext('2d');
      
      console.log(`提取瓦片，每个瓦片尺寸 ${tileWidth}x${tileHeight} -> ${tileSize}x${tileSize}`);
      
      // 生成六个面的瓦片
      const faces = ['f', 'b', 'l', 'r', 'u', 'd']; // 正面、背面、左侧、右侧、上方、下方
      
      for (const face of faces) {
        // 创建瓦片
        for (let y = 0; y < tilesPerRow; y++) {
          for (let x = 0; x < tilesPerRow; x++) {
            // 清除临时Canvas
            tileCtx.clearRect(0, 0, tileSize, tileSize);
            
            // 根据面的不同，调整提取瓦片的区域
            let sourceX = x * tileWidth;
            let sourceY = y * tileHeight;
            
            // 对不同的面进行适当偏移或变换
            // 注意：这是一个简化的模型，实际上应该根据球面投影算法计算每个面的映射
            switch (face) {
              case 'f': // 正面 - 使用原始图像中间部分
                sourceX = x * tileWidth + tileWidth * tilesPerRow * 0.25;
                break;
              case 'b': // 背面 - 使用原始图像中间部分的镜像
                sourceX = levelWidth - (x + 1) * tileWidth - tileWidth * tilesPerRow * 0.25;
                break;
              case 'l': // 左侧 - 使用原始图像左侧部分
                sourceX = x * tileWidth;
                break;
              case 'r': // 右侧 - 使用原始图像右侧部分
                sourceX = levelWidth - (x + 1) * tileWidth;
                break;
              case 'u': // 上方 - 使用原始图像上部
                sourceY = y * tileHeight * 0.5;
                break;
              case 'd': // 下方 - 使用原始图像下部
                sourceY = levelHeight - (y + 1) * tileHeight * 0.5;
                break;
            }
            
            // 确保坐标在有效范围内
            sourceX = Math.max(0, Math.min(levelWidth - tileWidth, sourceX));
            sourceY = Math.max(0, Math.min(levelHeight - tileHeight, sourceY));
            
            // 从大图中提取瓦片并绘制到临时Canvas
            tileCtx.drawImage(
              canvas,
              sourceX, sourceY, tileWidth, tileHeight,
              0, 0, tileSize, tileSize
            );
            
            // 将瓦片转换为数据URL
            const dataUrl = tileCanvas.toDataURL(`image/${format}`, quality);
            
            // 将瓦片添加到ZIP文件中
            const fileName = `${outputPrefix}${level}_${face}_${x}_${y}.${format}`;
            zip.file(fileName, dataUrl.substr(dataUrl.indexOf(',') + 1), { base64: true });
            
            console.log(`添加瓦片: ${fileName}`);
          }
        }
      }
      
      // 创建每个层级的预览图
      if (level === 0) {
        // 最低分辨率层级作为整体预览图
        const previewFileName = `preview.${format}`;
        const previewDataUrl = canvas.toDataURL(`image/${format}`, quality);
        zip.file(previewFileName, previewDataUrl.substr(previewDataUrl.indexOf(',') + 1), { base64: true });
        console.log(`添加预览图: ${previewFileName}`);
      }
    }
    
    // 生成并返回ZIP文件
    console.log('生成ZIP文件中...');
    const blob = await zip.generateAsync({ type: 'blob' });
    console.log(`ZIP文件生成完成，大小: ${(blob.size / (1024 * 1024)).toFixed(2)} MB`);
    
    return blob;
  } catch (err) {
    console.error('分割全景图时出错', err);
    throw err;
  }
}

/**
 * 下载Blob作为文件
 * @param {Blob} blob - 要下载的Blob
 * @param {string} fileName - 文件名
 */
export function downloadBlob(blob, fileName) {
  const url = URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = fileName;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
}

/**
 * 分割并下载全景图瓦片
 * @param {string} imageUrl - 全景图URL
 * @param {Object} options - 选项
 */
export async function processPanoramaAndDownload(imageUrl, options = {}) {
  try {
    // 在控制台显示生成六面全景的提示
    console.log(`开始生成全景六面瓦片，层级: ${options.levels}，每面瓦片数量：${Math.pow(2, options.levels) * Math.pow(2, options.levels)}`);
    console.log(`总瓦片数量：${6 * Math.pow(2, options.levels) * Math.pow(2, options.levels)}`);
    
    const blob = await createPanoramaTiles(imageUrl, options);
    downloadBlob(blob, options.outputFileName || 'panorama_tiles.zip');
    
    // 显示成功提示
    alert(`全景六面瓦片生成完成！
- 请将下载的ZIP文件解压到您的服务器目录
- 使用分片加载模式并指向解压目录即可
- 总瓦片数：${6 * Math.pow(2, options.levels) * Math.pow(2, options.levels)}`);
    
    return blob;
  } catch (err) {
    console.error('处理和下载全景图瓦片时出错', err);
    alert(`生成瓦片失败: ${err.message}`);
    throw err;
  }
} 